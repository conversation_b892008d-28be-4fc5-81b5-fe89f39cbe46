﻿/* Batch URL Cleaner Styles */

.url-input-container {
    position: relative;
}

.url-input {
    min-height: 200px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    resize: vertical;
}

.url-output {
    min-height: 200px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    white-space: pre-wrap;
    word-break: break-all;
}

.url-count {
    font-size: 0.875rem;
    color: #6c757d;
    position: absolute;
    top: 0.5rem;
    right: 0.75rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    z-index: 2;
}

.cleaning-options {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.option-group {
    margin-bottom: 1rem;
}

.option-group:last-child {
    margin-bottom: 0;
}

.custom-parameters-input {
    font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
}

.results-stats {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.results-stats .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.results-stats .stat-item:last-child {
    margin-bottom: 0;
}

.btn-copy {
    position: relative;
}

.btn-copy.copied::after {
    content: "Copied!";
    position: absolute;
    top: -2rem;
    left: 50%;
    transform: translateX(-50%);
    background: #28a745;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    10%, 90% { opacity: 1; }
}

.example-urls {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.8rem;
    color: #6c757d;
    background-color: #f8f9fa;
    border-left: 3px solid #007bff;
    padding: 0.75rem;
    margin-top: 0.5rem;
}

@media (max-width: 768px) {
    .url-input,
    .url-output {
        min-height: 150px;
        font-size: 0.8rem;
    }
    
    .cleaning-options {
        padding: 0.75rem;
    }
    
    .btn-group-vertical .btn {
        margin-bottom: 0.5rem;
    }
}
