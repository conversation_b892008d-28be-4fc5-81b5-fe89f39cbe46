﻿import { html, render, useState, useEffect, useRef } from 'preact';
import ChartBuilder from './ChartBuilder.js';
import SettingCard, { defaultSettings } from './SettingCard.js';

const App = () => {
    const canvasRef = useRef(null);

    const handleGenerate = (settings) => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        new ChartBuilder(canvas, settings).build();

        const convasCard = canvas.parentElement.parentElement;
        convasCard.scrollIntoView({ behavior: 'smooth' });
    };

    const printCanvasAsBlob = () => {
        const canvas = canvasRef.current;
        if (!canvas) return;
        
        canvas.toBlob(function(blob) {
            const url = URL.createObjectURL(blob);
            const printWindow = window.open(url, '_blank');
            
            printWindow.onload = function() {
                printWindow.print();
                printWindow.onafterprint = function() {
                    URL.revokeObjectURL(url);
                    printWindow.close();
                };
            };
        }, 'image/jpeg');
    };

    useEffect(() => handleGenerate(defaultSettings), [canvasRef]);

    const handleDownload = () => {
        const canvas = canvasRef.current;
        if (!canvas) return;
        const link = document.createElement('a');
        link.download = 'blood-pressure-chart.jpg';
        link.href = canvas.toDataURL('image/jpeg');
        link.click();
    };

    return html`
        <div class="tool-content">
            <${SettingCard} onGenerate=${handleGenerate} />
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Chart</h5>
                    <div class="actions">
                        <button class="btn btn-outline-secondary me-2" onClick=${handleDownload}>
                            <i class="bi bi-download me-1"></i>Download
                        </button>
                        <button class="btn btn-outline-primary" onClick=${printCanvasAsBlob}>
                            <i class="bi bi-printer me-1"></i>Print
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <canvas ref=${canvasRef} class="a4-canvas"></canvas>
                </div>
            </div>
        </div>
    `;
};

render(html`<${App} />`, document.getElementById('app'));
