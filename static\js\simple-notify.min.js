﻿function t(t,e){if(!(t instanceof e)){throw new TypeError("Cannot call a class as a function")}}function e(t,e){for(var s=0;s<e.length;s++){var i=e[s];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(t,i.key,i)}}function s(t,s,i){if(s)e(t.prototype,s);if(i)e(t,i);return t}(function(){var e=Object.defineProperty;var i=function(t,s){return e(t,"name",{value:s,configurable:!0})};var n='<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">\r\n  <path d="m8.94 8 4.2-4.193a.67.67 0 0 0-.947-.947L8 7.06l-4.193-4.2a.67.67 0 1 0-.947.947L7.06 8l-4.2 4.193a.667.667 0 0 0 .217 1.093.666.666 0 0 0 .73-.146L8 8.94l4.193 4.2a.666.666 0 0 0 1.094-.217.665.665 0 0 0-.147-.73L8.94 8Z" fill="currentColor"/>\r\n</svg>\r\n';var o='<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">\r\n  <path d="M16 2.667a13.333 13.333 0 1 0 0 26.666 13.333 13.333 0 0 0 0-26.666Zm0 24A10.667 10.667 0 0 1 5.333 16a10.56 10.56 0 0 1 2.254-6.533l14.946 14.946A10.56 10.56 0 0 1 16 26.667Zm8.413-4.134L9.467 7.587A10.56 10.56 0 0 1 16 5.333 10.667 10.667 0 0 1 26.667 16a10.56 10.56 0 0 1-2.254 6.533Z" fill="currentColor"/>\r\n</svg>\r\n';var a='<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">\r\n  <path d="M16 14.667A1.333 1.333 0 0 0 14.667 16v5.333a1.333 1.333 0 0 0 2.666 0V16A1.333 1.333 0 0 0 16 14.667Zm.507-5.227a1.333 1.333 0 0 0-1.014 0 1.334 1.334 0 0 0-.44.28 1.56 1.56 0 0 0-.28.44c-.075.158-.11.332-.106.507a1.332 1.332 0 0 0 .386.946c.13.118.279.213.44.28a1.334 1.334 0 0 0 1.84-1.226 1.4 1.4 0 0 0-.386-.947 1.334 1.334 0 0 0-.44-.28ZM16 2.667a13.333 13.333 0 1 0 0 26.666 13.333 13.333 0 0 0 0-26.666Zm0 24a10.666 10.666 0 1 1 0-21.333 10.666 10.666 0 0 1 0 21.333Z" fill="currentColor"/>\r\n</svg>\r\n';var r='<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">\r\n  <path d="m19.627 11.72-5.72 5.733-2.2-2.2a1.334 1.334 0 1 0-1.88 1.881l3.133 3.146a1.333 1.333 0 0 0 1.88 0l6.667-6.667a1.333 1.333 0 1 0-1.88-1.893ZM16 2.667a13.333 13.333 0 1 0 0 26.666 13.333 13.333 0 0 0 0-26.666Zm0 24a10.666 10.666 0 1 1 0-21.333 10.666 10.666 0 0 1 0 21.333Z" fill="currentColor"/>\r\n</svg>\r\n';var c='<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">\r\n  <path d="M16.334 17.667a1.334 1.334 0 0 0 1.334-1.333v-5.333a1.333 1.333 0 0 0-2.665 0v5.333a1.333 1.333 0 0 0 1.33 1.333Zm-.508 5.227c.325.134.69.134 1.014 0 .165-.064.314-.159.44-.28a1.56 1.56 0 0 0 .28-.44c.076-.158.112-.332.107-.507a1.332 1.332 0 0 0-.387-.946 1.532 1.532 0 0 0-.44-.28 1.334 1.334 0 0 0-1.838 1.226 1.4 1.4 0 0 0 .385.947c.127.121.277.216.44.28Zm.508 6.773a13.333 13.333 0 1 0 0-26.667 13.333 13.333 0 0 0 0 26.667Zm0-24A10.667 10.667 0 1 1 16.54 27a10.667 10.667 0 0 1-.206-21.333Z" fill="currentColor"/>\r\n</svg>\r\n';var l=i(function(t){return new DOMParser().parseFromString(t,"text/html").body.childNodes[0]},"stringToHTML"),h=i(function(t){var e=new DOMParser().parseFromString(t,"application/xml");return document.importNode(e.documentElement,!0).outerHTML},"getSvgNode");var d={CONTAINER:"sn-notifications-container",NOTIFY:"sn-notify",NOTIFY_CONTENT:"sn-notify-content",NOTIFY_ICON:"sn-notify-icon",NOTIFY_CLOSE:"sn-notify-close",NOTIFY_TITLE:"sn-notify-title",NOTIFY_TEXT:"sn-notify-text",IS_X_CENTER:"sn-is-x-center",IS_Y_CENTER:"sn-is-y-center",IS_CENTER:"sn-is-center",IS_LEFT:"sn-is-left",IS_RIGHT:"sn-is-right",IS_TOP:"sn-is-top",IS_BOTTOM:"sn-is-bottom",NOTIFY_OUTLINE:"sn-notify-outline",NOTIFY_FILLED:"sn-notify-filled",NOTIFY_ERROR:"sn-notify-error",NOTIFY_WARNING:"sn-notify-warning",NOTIFY_SUCCESS:"sn-notify-success",NOTIFY_INFO:"sn-notify-info",NOTIFY_FADE:"sn-notify-fade",NOTIFY_FADE_IN:"sn-notify-fade-in",NOTIFY_SLIDE:"sn-notify-slide",NOTIFY_SLIDE_IN:"sn-notify-slide-in",NOTIFY_AUTOCLOSE:"sn-notify-autoclose"},u={ERROR:"error",WARNING:"warning",SUCCESS:"success",INFO:"info"},f={OUTLINE:"outline",FILLED:"filled"},p={FADE:"fade",SLIDE:"slide"},I={CLOSE:h(n),SUCCESS:h(r),ERROR:h(o),WARNING:h(c),INFO:h(a)};var v=i(function(t){t.wrapper.classList.add(d.NOTIFY_FADE),setTimeout(function(){t.wrapper.classList.add(d.NOTIFY_FADE_IN)},100)},"fadeIn"),N=i(function(t){t.wrapper.classList.remove(d.NOTIFY_FADE_IN),setTimeout(function(){t.wrapper.remove()},t.speed)},"fadeOut"),O=i(function(t){t.wrapper.classList.add(d.NOTIFY_SLIDE),setTimeout(function(){t.wrapper.classList.add(d.NOTIFY_SLIDE_IN)},100)},"slideIn"),T=i(function(t){t.wrapper.classList.remove(d.NOTIFY_SLIDE_IN),setTimeout(function(){t.wrapper.remove()},t.speed)},"slideOut");var E=function(){"use strict";function e(s){var n=this;t(this,e);this.notifyOut=i(function(t){t(n)},"notifyOut");var o=s.notificationsGap,a=o===void 0?20:o,r=s.notificationsPadding,c=r===void 0?20:r,l=s.status,h=l===void 0?"success":l,d=s.effect,u=d===void 0?p.FADE:d,f=s.type,I=f===void 0?"outline":f,v=s.title,N=s.text,O=s.showIcon,T=O===void 0?!0:O,E=s.customIcon,m=E===void 0?"":E,w=s.customClass,y=w===void 0?"":w,L=s.speed,C=L===void 0?500:L,F=s.showCloseButton,_=F===void 0?!0:F,S=s.autoclose,g=S===void 0?!0:S,R=s.autotimeout,Y=R===void 0?3e3:R,A=s.position,x=A===void 0?"right top":A,b=s.customWrapper,k=b===void 0?"":b;if(this.customWrapper=k,this.status=h,this.title=v,this.text=N,this.showIcon=T,this.customIcon=m,this.customClass=y,this.speed=C,this.effect=u,this.showCloseButton=_,this.autoclose=g,this.autotimeout=Y,this.notificationsGap=a,this.notificationsPadding=c,this.type=I,this.position=x,!this.checkRequirements()){console.error("You must specify 'title' or 'text' at least.");return}this.setContainer(),this.setWrapper(),this.setPosition(),this.showIcon&&this.setIcon(),this.showCloseButton&&this.setCloseButton(),this.setContent(),this.container.prepend(this.wrapper),this.setEffect(),this.notifyIn(this.selectedNotifyInEffect),this.autoclose&&this.autoClose(),this.setObserver()}s(e,[{key:"checkRequirements",value:function t(){return!!(this.title||this.text)}},{key:"setContainer",value:function t(){var t=document.querySelector(".".concat(d.CONTAINER));t?this.container=t:(this.container=document.createElement("div"),this.container.classList.add(d.CONTAINER),document.body.appendChild(this.container)),this.notificationsPadding&&this.container.style.setProperty("--sn-notifications-padding","".concat(this.notificationsPadding,"px")),this.notificationsGap&&this.container.style.setProperty("--sn-notifications-gap","".concat(this.notificationsGap,"px"))}},{key:"setPosition",value:function t(){this.container.classList[this.position==="center"?"add":"remove"](d.IS_CENTER),this.container.classList[this.position.includes("left")?"add":"remove"](d.IS_LEFT),this.container.classList[this.position.includes("right")?"add":"remove"](d.IS_RIGHT),this.container.classList[this.position.includes("top")?"add":"remove"](d.IS_TOP),this.container.classList[this.position.includes("bottom")?"add":"remove"](d.IS_BOTTOM),this.container.classList[this.position.includes("x-center")?"add":"remove"](d.IS_X_CENTER),this.container.classList[this.position.includes("y-center")?"add":"remove"](d.IS_Y_CENTER)}},{key:"setCloseButton",value:function t(){var t=this;var e=document.createElement("div");e.classList.add(d.NOTIFY_CLOSE),e.innerHTML=I.CLOSE,this.wrapper.appendChild(e),e.addEventListener("click",function(){t.close()})}},{key:"setWrapper",value:function t(){var t=this;switch(this.customWrapper?this.wrapper=l(this.customWrapper):this.wrapper=document.createElement("div"),this.wrapper.style.setProperty("--sn-notify-transition-duration","".concat(this.speed,"ms")),this.wrapper.classList.add(d.NOTIFY),this.type){case f.OUTLINE:this.wrapper.classList.add(d.NOTIFY_OUTLINE);break;case f.FILLED:this.wrapper.classList.add(d.NOTIFY_FILLED);break;default:this.wrapper.classList.add(d.NOTIFY_OUTLINE)}switch(this.status){case u.SUCCESS:this.wrapper.classList.add(d.NOTIFY_SUCCESS);break;case u.ERROR:this.wrapper.classList.add(d.NOTIFY_ERROR);break;case u.WARNING:this.wrapper.classList.add(d.NOTIFY_WARNING);break;case u.INFO:this.wrapper.classList.add(d.NOTIFY_INFO);break}this.autoclose&&(this.wrapper.classList.add(d.NOTIFY_AUTOCLOSE),this.wrapper.style.setProperty("--sn-notify-autoclose-timeout","".concat(this.autotimeout+this.speed,"ms"))),this.customClass&&this.customClass.split(" ").forEach(function(e){t.wrapper.classList.add(e)})}},{key:"setContent",value:function t(){var t=document.createElement("div");t.classList.add(d.NOTIFY_CONTENT);var e,s;this.title&&(e=document.createElement("div"),e.classList.add(d.NOTIFY_TITLE),e.textContent=this.title.trim(),this.showCloseButton||(e.style.paddingRight="0")),this.text&&(s=document.createElement("div"),s.classList.add(d.NOTIFY_TEXT),s.innerHTML=this.text.trim(),this.title||(s.style.marginTop="0")),this.wrapper.appendChild(t),this.title&&t.appendChild(e),this.text&&t.appendChild(s)}},{key:"setIcon",value:function t(){var t=i(function(t){switch(t){case u.SUCCESS:return I.SUCCESS;case u.ERROR:return I.ERROR;case u.WARNING:return I.WARNING;case u.INFO:return I.INFO}},"computedIcon"),e=document.createElement("div");e.classList.add(d.NOTIFY_ICON),e.innerHTML=this.customIcon||t(this.status),(this.status||this.customIcon)&&this.wrapper.appendChild(e)}},{key:"setObserver",value:function t(){var t=this;var e=new IntersectionObserver(function(e){if(e[0].intersectionRatio<=0)t.close();else return},{threshold:0});setTimeout(function(){e.observe(t.wrapper)},this.speed)}},{key:"notifyIn",value:function t(t){t(this)}},{key:"autoClose",value:function t(){var t=this;setTimeout(function(){t.close()},this.autotimeout+this.speed)}},{key:"close",value:function t(){this.notifyOut(this.selectedNotifyOutEffect)}},{key:"setEffect",value:function t(){switch(this.effect){case p.FADE:this.selectedNotifyInEffect=v,this.selectedNotifyOutEffect=N;break;case p.SLIDE:this.selectedNotifyInEffect=O,this.selectedNotifyOutEffect=T;break;default:this.selectedNotifyInEffect=v,this.selectedNotifyOutEffect=N}}}]);return e}();i(E,"Notify");var m=E;globalThis.Notify=m})();//# sourceMappingURL=simple-notify.min.js.map