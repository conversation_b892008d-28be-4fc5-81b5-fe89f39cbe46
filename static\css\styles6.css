﻿/* CodeMirror whitespace highlighting using custom overlay */
.CodeMirror .cm-whitespace-space {
    position: relative;
    background: transparent;
}

.CodeMirror .cm-whitespace-space::after {
    content: '·';
    color: #cfcfcf;
    font-weight: bold;
    font-size: 14px;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.CodeMirror .cm-whitespace-tab {
    position: relative;
    background: transparent;
}

.CodeMirror .cm-whitespace-tab::after {
    content: '→';
    color: #0d6efd;
    font-weight: bold;
    font-size: 12px;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.CodeMirror .cm-whitespace-fullwidth-space {
    position: relative;
    background: transparent;
}

.CodeMirror .cm-whitespace-fullwidth-space::after {
    content: 'ㅇ';
    color: #cfcfcf;
    font-weight: bold;
    font-size: 14px;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.CodeMirror .cm-whitespace-newline {
    position: relative;
    background: transparent;
}

.CodeMirror .cm-whitespace-newline::after {
    content: '¶';
    color: #198754;
    font-weight: bold;
    font-size: 12px;
    position: absolute;
    right: 0;
    top: 0;
    width: auto;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.CodeMirror .cm-whitespace-line-end::after {
    content: '¶';
    color: #198754;
    font-weight: bold;
    font-size: 12px;
    margin-left: 2px;
    opacity: 0.7;
}

.CodeMirror .cm-whitespace-empty-line::after {
    content: '¶';
    color: #198754;
    font-weight: bold;
    font-size: 12px;
    opacity: 0.7;
}

/* Add newline marker for empty lines using cm-text spans */
.CodeMirror span[cm-text=""]::after {
    content: '¶';
    color: #198754;
    font-weight: bold;
    font-size: 12px;
    opacity: 0.7;
    margin-left: 2px;
}

.CodeMirror-focused {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.CodeMirror {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    font-family: monospace;
    height: auto;
    min-height: 200px;
}

.codemirror-container {
    min-height: 200px;
}
