﻿<!DOCTYPE html>
<html lang="en">

<head>
    
        <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-ZZ4LKYJ35E"></script>
<script>
window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());

gtag('config', 'G-ZZ4LKYJ35E');
</script>
    

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    
    

    
        
    

    
    

    <title>Number Images | Feather Tools</title>

    <meta name="description" content="Batch add sequence numbers to images with privacy - all processing happens in your browser.">
    <meta name="keywords" content="image generator, numbering, sequence, privacy, client-side, online tools, free, lightweight, productivity, image tools">
    <meta name="author" content="Feather Tools">

    <meta property="og:title" content="Number Images | Feather Tools">
    <meta property="og:description" content="Batch add sequence numbers to images with privacy - all processing happens in your browser.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://feathertools.top/number-images">
    <meta property="og:site_name" content="Feather Tools">

    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Number Images | Feather Tools">
    <meta name="twitter:description" content="Batch add sequence numbers to images with privacy - all processing happens in your browser.">

    <link rel="apple-touch-icon" sizes="180x180" href="/static/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/static/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- SEO -->
    <link rel="sitemap" type="application/xml" title="Sitemap" href="/sitemap.xml">

    <link href="static/css/bootstrap.min.css" integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous" rel="stylesheet">
    <link rel="stylesheet" href="static/css/bootstrap-icons.css">
    <link rel="stylesheet" href="static/css/simple-notify.min.css">
    <script src="static/js/bootstrap.bundle.min.js" integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO" crossorigin="anonymous"></script>
    <script src="static/js/simple-notify.min.js" integrity="sha256-5oyRx5pR3Tpi4tN9pTtnN5czAU1ElI2sUbaRQsxjAEY=" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="static/css/styles.css">
    <script type="importmap">
    {
        "imports": {
            "preact": "/static/libs/preact.js",
            "qrcode": "https://cdn.jsdelivr.net/npm/qrcode@1.5.4/+esm",
            "axios": "https://cdn.jsdelivr.net/npm/axios@1.9.0/+esm",
            "jszip": "https://cdn.jsdelivr.net/npm/jszip@3.10.1/+esm"
        }
    }
    </script>

    
    <link rel="stylesheet" href="static/css/styles4.css">

</head>

<body>
    <nav class="navbar navbar-expand-lg bg-body-tertiary">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="index.html">
                <img src="static/picture/favicon-32x32.png" alt="Logo" width="24" height="24" class="me-2">
                Feather Tools
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html#image-tools">Image Tools</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.html#text-tools">Text Tools</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <main class="container py-4">
        <div class="row">
            
            <div class="col-12">
            
                
    <h1 class="mb-4">Number Images</h1>
    <p class="lead mb-5">Batch add sequence numbers to images with privacy - all processing happens in your browser.</p>

    <div id="app">
    <div class="d-flex align-items-center mb-4">
        <strong role="status">Loading...</strong>
        <div class="spinner-border ms-auto" aria-hidden="true"></div>
    </div>
</div>

    <div class="row row-gap-4 mb-4">
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">How to Use</h5>
                </div>
                <div class="card-body">
                    <ol class="mb-0">
                        <li><strong>Select images</strong> - Drag & drop or paste images directly into your browser (all processing happens locally)</li>
                        <li>Choose your preferred <strong>number style</strong> - Select between numbers (1,2,3) or letters (A,B,C)</li>
                        <li>Adjust <strong>visual settings</strong> - Customize position, size, colors, font, and border</li>
                        <li>Set the <strong>number range</strong> - Define start number and sequence pattern</li>
                        <li><strong>Preview & generate</strong> - See changes instantly before downloading</li>
                        <li><strong>Download options</strong> - Save individual images or get all as a ZIP file</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Features</h5>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li><strong>Privacy-First Processing</strong> - All image processing happens directly in your browser, with no data sent to any servers</li>
                        <li><strong>Customizable Design</strong> - Full control over colors, fonts, sizes, and positioning</li>
                        <li><strong>Flexible Numbering</strong> - Supports numbers (1,2,3), letters (A,B,C), and custom sequences</li>
                        <li><strong>Multiple Formats</strong> - Support for high-quality PNG, JPG, and WebP</li>
                        <li><strong>Batch Download</strong> - Download all processed images in one click as a ZIP file</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <h5 class="mb-3">You may also like</h5>
    <div class="row row-gap-4 mb-4">
        <div class="col-lg-6">
            
            <div class="card card-tool h-100">
    <div class="card-body">
        <h5 class="card-title">
            <a href="rich-qrcode.html" class="stretched-link">Rich QRCode</a>
        </h5>
        <p class="card-text">Generate enhanced QR code cards with title and URL information alongside the QR code.</p>
    </div>
</div>
        </div>
        <div class="col-lg-6">
            
            <div class="card card-tool h-100">
    <div class="card-body">
        <h5 class="card-title">
            <a href="printable-blood-pressure-chart.html" class="stretched-link">Printable Blood Pressure Chart</a>
        </h5>
        <p class="card-text">Generate and print monthly/weekly/bi-weekly blood pressure tracking charts for monitoring health trends over time.</p>
    </div>
</div>
        </div>
    </div>

            </div>
        </div>
    </main>
    
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2025 Feather Tools. All rights reserved.</span>
        </div>
    </footer>

    
  <script src="static/js/main3.js" type="module"></script>

</body>

</html>