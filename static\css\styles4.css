﻿/* Number Images Tool Styles */

.upload-zone {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.upload-zone:hover {
    border-color: #0d6efd;
    background-color: #f0f7ff;
}

.upload-zone.dragover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
}

.upload-content {
    pointer-events: none;
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.upload-thumbnail {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    flex-shrink: 0;
}

.image-list-item:hover {
    background-color: #f8f9fa;
}

.image-list-item .flex-grow-1 {
    min-width: 0;
    overflow: hidden;
}

.image-list-item.dragging {
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    outline: 1px solid #0d6efd;
    z-index: 100;
}
.image-list-item.dragging .image-remove {
    visibility: hidden;
}
